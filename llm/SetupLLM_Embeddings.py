"""
Model factory for creating LLM and embedding models from different providers.
"""
import os

from langchain.agents import initialize_agent, AgentType
from langchain_core.embeddings import Embeddings
from langchain_core.language_models import BaseChatModel

from Constants.Config import Settings
from utils.LogUtils import logger


class ModelFactory:
    """Factory class for creating models from different providers."""

    @staticmethod
    def create_llm(settings: Settings) -> BaseChatModel:
        """Create LLM based on the configured provider."""
        if settings.model_provider.lower() == "openai":
            return ModelFactory._create_openai_llm(settings)
        elif settings.model_provider.lower() == "ollama":
            return ModelFactory._create_ollama_llm(settings)
        else:
            raise ValueError(f"Unsupported model provider: {settings.model_provider}")

    @staticmethod
    def create_embeddings(settings: Settings) -> Embeddings:
        """Create embeddings based on the configured provider."""
        if settings.embedding_provider.lower() == "openai":
            return ModelFactory._create_openai_embeddings(settings)
        elif settings.embedding_provider.lower() == "ollama":
            return ModelFactory._create_ollama_embeddings(settings)
        else:
            raise ValueError(f"Unsupported embedding provider: {settings.model_provider}")

    @staticmethod
    def _create_openai_llm(settings: Settings) -> BaseChatModel:
        """Create Ollama LLM."""
        try:
            from langchain_openai import AzureChatOpenAI

            llm = AzureChatOpenAI(
                azure_deployment=settings.openai_model_name,
                api_version="2025-01-01-preview",
                temperature=0.2,
            )
            logger.info(f"✅ OpenAI LLM initialized: {settings.openai_model_name}")
            return llm
        except ImportError:
            raise ImportError("langchain_openai is required for OpenAI models. Install with: pip install langchain-openai")
        except Exception as e:
            raise RuntimeError(f"Failed to initialize OpenAI LLM: {e}")

    @staticmethod
    def _create_ollama_llm(settings: Settings) -> BaseChatModel:
        """Create Ollama LLM."""
        try:
            from langchain_ollama import ChatOllama

            llm = ChatOllama(
                model=settings.ollama_model_name,
                base_url=settings.ollama_base_url,
                temperature=0.0,
                extract_reasoning=False,
                num_predict=-1,
                format="json",
            )
            logger.info(f"✅ Ollama LLM initialized: {settings.ollama_model_name}")
            return llm
        except ImportError:
            raise ImportError("langchain_ollama is required for Ollama models. Install with: pip install langchain-ollama")
        except Exception as e:
            raise RuntimeError(f"Failed to initialize Ollama LLM: {e}")

    @staticmethod
    def _create_openai_embeddings(settings: Settings) -> Embeddings:
        """Create Ollama embeddings."""
        try:
            from langchain_openai import AzureOpenAIEmbeddings

            embeddings = AzureOpenAIEmbeddings(
                model=settings.openai_embedding_model,
                api_version="2025-01-01-preview",
            )
            logger.info(f"✅ OpenAI embeddings initialized: {settings.openai_embedding_model}")
            return embeddings
        except ImportError:
            raise ImportError(
                "langchain-openai is required for OpenAI embeddings. Install with: pip install langchain-openai")
        except Exception as e:
            raise RuntimeError(f"Failed to initialize OpenAI embeddings: {e}")

    @staticmethod
    def _create_ollama_embeddings(settings: Settings) -> Embeddings:
        """Create Ollama embeddings."""
        try:
            from langchain_ollama.embeddings import OllamaEmbeddings

            embeddings = OllamaEmbeddings(
                model=settings.ollama_embedding_model,
                base_url=settings.ollama_base_url
            )
            logger.info(f"✅ Ollama embeddings initialized: {settings.ollama_embedding_model}")
            return embeddings
        except ImportError:
            raise ImportError(
                "langchain_ollama is required for Ollama embeddings. Install with: pip install langchain-ollama")
        except Exception as e:
            raise RuntimeError(f"Failed to initialize Ollama embeddings: {e}")


def get_models(settings: Settings) -> tuple[BaseChatModel, Embeddings]:
    """Get both LLM and embeddings models."""
    llm = ModelFactory.create_llm(settings)
    embeddings = ModelFactory.create_embeddings(settings)
    return llm, embeddings
