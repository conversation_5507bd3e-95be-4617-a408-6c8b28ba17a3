"""
Policy field mappings for different insurance policy types.
"""

forms_and_endorsements = ["FORMS SCHEDULE", "SCHEDULE OF FORMS AND ENDORSEMENTS", "SCHEDULE OF UNDERLYING INSURANCE"]

POLICY_FIELD_MAPPINGS = {
    "Applied Aviation Corporate Aircraft": {
        "policy number": "string",
        "Previous policy number/Renewal of Policy": "string",
        "Named Insured": "string",
        "Address/Mailing Address": "string",
        "Producer Name/ Producing Agency": "string",
        "Policy Period": "FROM YYYY-MM-DD TO YYYY-MM-DD",
        "Pilots (List of names Pilot-In-Command)": ["name of pilots"],
        "Pilot Warranty Endorsement (Lists of requirements and logged hours for pilots)": "string",
        "Schedule of Forms and Endorsements": "dict array",
        "Total Annual Premium (including Terrorism, Tax, and Fees)": "float",
        "Liability Premium": "float",
        "Physical Damage Premium": "float",
        "Terrorism (Included, Not covered, Rejected, or numeric value $)": "string",
        "Total Fees & Surcharges": "float",
        "Liability for Scheduled Aircraft":
            [
                {
                    "FAA Cert. Number": "string",
                    "Make & Model": "string",
                    "Year Built": "YYYY",
                    "SeatsCrew / Pass": "Int/Int",
                    "Aircraft Liability Limit": "float"
                }
            ],
        "Liability for the Use of Non-Owned Aircraft": "float",
        "Liability for Property Damage to Non-Owned Aircraft and Temporary Substitute Aircraft": "float",
        "Liability for Charter Referral": "float",
        "Liability for Charter Referral Details":
            {
                "Scheduled Aircraft or Temporary Substitute Aircraft-Each Non-Crew Member": "float",
                "Scheduled Aircraft or Temporary Substitute Aircraft-Each Crew Member": "float",
                "Non-Owned Aircraft except a Temporary Substitute Aircraft-Each Non-Crew Member": "float",
                "Non-Owned Aircraft except a Temporary Substitute Aircraft-Each Crew Member": "float",
                "Total All Non-Owned Aircraft Crew Members and Non-Crew Member Passengers Combined": "float",
                "Maximum Weekly Indemnity Limit": "float",
                "Maximum Indemnity Period": "float"
            },
        "Liability for Property Damage to Hangars and Their Contents": "float",
        "Liability for Fire Damage to Real Property": "float",
        "Liability for Cargo": "float",
        "Liability for Cargo details": {"Deductible": "float"},
        "Liability Under Contractual Agreements": "float",
        "Liability For Alcohol Beverage Service": "float",
        "Liability for Incidental Medical Malpractice": "float",
        "Liability for the Use of Premises": "float",
        "Liability for the Operation of Mobile Equipment": "float",
        "Liability for the Operation of an Auto while on Airport Premises": "float",
        "Liability for the Sale of Aircraft and Aircraft Products and Services": "float",
        "Liability for the Sale of Aircraft and Aircraft Products and Services Details":
            {
                "Each Aircraft": "float",
                "Each Occurrence": "float",
                "Deductible Each Aircraft": "float",
                "Deductible Each Occurrence": "float",
                "Any One Auto": "float",
                "Any One Loss": "float",
                "Deductible Each Auto": "float"
            },
        "Physical Damage Coverage for Scheduled Aircraft (including Ingestion and Emergency Landing)":
            [
                {
                    "FAA Cert. Number": "string",
                    "Make & Model": "string",
                    "Insured Value": "float",
                    "Deductible-Not In Motion": "float",
                    "Deductible-In Motion/Ingestion": "float"
                }
            ],
        "Physical Damage Coverage for Spare Engines and Spare Parts Including Transit": "float",
        "Physical Damage Coverage for Spare Engines and Spare Parts Including Transit Details":
            {
                "Deductible Not In Motion": "float",
                "Deductible In Motion": "float",
                "Maximum Automatic Physical Damage Limit for Scheduled Aircraft": "float",
                "Maximum Automatic Physical Damage Limit for Spare Engines and Spare Parts": "float",
                "Each_Occurrence": "float",
                "Each Employee": "float",
                "Deductible: $ Each Employee/Each Occurrence": "float"
            },
        "Temporary Replacement Parts Rental Expense": "float",
        "Temporary Replacement Parts Rental Expense Details":
            {
                "Minimum Repair Period": "float",
                "Maximum Coverage Period": "float"
            },
        "Replacement Aircraft Rental Expense": "float",
        "Replacement Aircraft Rental Expense Details":
            {
                "Minimum-Repair Period": "float",
                "Maximum-Coverage Period": "float"
            },
        "Search and Rescue Expenses": "float",
        "Runway Foaming and Crash Control Expenses": "float",
        "Trip Interruption Expense Coverage": "float",
        "Automatic Insurance for Newly Acquired Aircraft": "float",
        "Lay-Up Credit for Scheduled Aircraft": "string",
        "Personal Effects and Baggage Expense": "float",
        "Personal Effects and Baggage Expense Details":
            {
                "Scheduled Aircraft or Temporary Substitute Aircraft-Each Non-Crew Member Passenger": "float",
                "Scheduled Aircraft or Temporary Substitute Aircraft-Each Crew Member Passenger": "float",
                "Non-Owned Aircraft except a Temporary Substitute Aircraft-Each Non-Crew Member Passenger": "float",
                "Non-Owned Aircraft except a Temporary Substitute Aircraft-Each Crew Member Passenger": "float",
                "Each-Occurrence": "float",
                "Each Person": "float"
            },
        "Personal & Advertising Injury Aggregate Limit": "float"
    },

    "Applied Aviation Commercial General Liability": {
        "policy number": "string",
        "Previous policy number/Renewal of Policy": "string",
        "Named Insured": "string",
        "Address/Mailing Address": "string",
        "Policy Period": "FROM YYYY-MM-DD TO YYYY-MM-DD",
        "Description of Business or Premises": "string",
        "All Premises You Own, Rent, or Occupy": "string",
        "Schedule of Forms and Endorsements": "dict array",
        "Total Annual Premium (including Terrorism, Tax, and Fees)": "float",
        "Liability Premium": "float",
        "Terrorism (Included, Not covered, Rejected, or numeric value $)": "string or float",
        "Total Fees & Surcharges": "float",
        "Liability for Hangarkeeper Operations": "float",
        "Each Occurrence Limit/POLICY LIMIT": "float",
        "Damage To Premises Rented To You Limit": "float",
        "Medical Expense Limit/Medical Payments": "float",
        "Personal & Advertising Injury Aggregate Limit": "float",
        "General Aggregate Limit": "float",
        "Products/Completed Operations Aggregate Limit": "float",
        "Each aircraft Limit": "float",
        "Each Loss Limit": "float",
        "Hangarkeeper's Deductible": "float"
    },
    "Applied Specialty Underwriters Commercial Excess Liability": {
        "policy number": "string",
        "Previous policy number/Renewal of Policy": "string",
        "Named Insured": "string",
        "Address/Mailing Address": "string",
        "Producer Name/ Producing Agency": "string",
        "Policy Period": "FROM YYYY-MM-DD TO YYYY-MM-DD",
        "Issuing Company/ Issuing Carrier/Insurer": "string",
        "Description of Business or Premises": "string",
        "Audit Period": "string",
        "Schedule of Forms and Endorsements": "[{Header1 : value, Header2 : value...},{Header1 : value,..}..]",
        "Total Annual Premium (including Terrorism, Tax, and Fees)": "float",
        "Written Premium (Premium before TRIA, Taxes, and Fees)": "float",
        "Terrorism (Included, Not covered, Rejected, or numeric value $)": "string or float",
        "Total Fees & Surcharges": "float",
        "Premium Basis": "string",
        "Estimated Exposures": "string",
        "Each Occurrence Limit/ POLICY LIMIT": "float",
        "General Aggregate Limit": "float",
        "Products/Completed Operations Aggregate Limit": "float",
        "underlying_details": [{
            "Coverage": "string",
            "Carrier/ Controlling Underlying Carrier": "string",
            "policy number/ Controlling Underlying Policy": "string",
            "Policy Period": "FROM YYYY-MM-DD TO YYYY-MM-DD",
            "Limits": "float",
            "Aggregate Limit": "float",
            "Each Occurrence Limit": "float",
            "Position": "string",
            "Products/Completed Operations Aggregate Limit": "float",
            "Personal & Advertising Injury Limit": "float/string"
        }]
    },

    "Outfitters & Guides Commercial General Liability": {
        "policy number": "string",
        "Previous policy number/Renewal of Policy": "string",
        "Named Insured": "string",
        "Address/Mailing Address": "string",
        "Producer Name/ Producing Agency": "string",
        "Policy Period": "FROM YYYY-MM-DD TO YYYY-MM-DD",
        "Issuing Company/ Issuing Carrier/Insurer": "string",
        "Broker/Surplus Broker": "string",
        "Description of Business or Premises": "string",
        "Retroactive Date": "YYYY-MM-DD",
        "All Premises You Own, Rent, or Occupy": "string",
        "Schedule of Forms and Endorsements": "dict array",
        "Total Annual Premium (including Terrorism, Tax, and Fees)": "float",
        "Liability Premium": "float",
        "Terrorism (Included, Not covered, Rejected, or numeric value $)": "string or float",
        "Total Taxes": "float",
        "Total Fees & Surcharges": "float",
        "Premium Basis": "string",
        "exposure_details": {
            "Estimated Exposures": "string",
            "Each Occurrence Limit/ POLICY LIMIT": "float",
            "Damage To Premises Rented To You Limit": "float",
            "Medical Expense Limit / Medical Payments": "float",
            "Personal & Advertising Injury Aggregate Limit": "float",
            "General Aggregate Limit": "float",
            "Products/Completed Operations Aggregate Limit": "float",
            "Deductible": "float",
            "Deductible Type": "string"
        }
    },

    "Applied Entertainment Sport Excess Liability": {
        "policy number": "string",
        "Previous policy number/Renewal of Policy": "string",
        "Named Insured": "string",
        "Address/Mailing Address": "string",
        "Policy Period": "FROM YYYY-MM-DD TO YYYY-MM-DD",
        "Issuing Company/ Issuing Carrier/Insurer": "string",
        "Broker/Surplus Broker": "string",
        "Schedule of Forms and Endorsements": "dict array",
        "Total Annual Premium (including Terrorism, Tax, and Fees)": "float",
        "Written Premium (Premium before TRIA, Taxes, and Fees)": "float",
        "Terrorism (Included, Not covered, Rejected, or numeric value $)": "string or float",
        "Total Taxes": "float or string (Fill Non N/A values like 'Broker Filing', 'Agent Filing' etc if present)",
        "Total Fees & Surcharges": "float",
        "Each Occurrence Limit/ POLICY LIMIT": "float",
        "underlying_details": [{
            "coverage": "string",
            "Carrier/ Controlling Underlying Carrier": "string",
            "policy number/ Controlling Underlying Policy": "string",
            "Policy Period": "FROM YYYY-MM-DD TO YYYY-MM-DD",
            "Aggregate Limit": "float",
            "Each Occurrence Limit": "float",
            "Position": "string"
        }]
    },

    "Applied Entertainment Sports General Liability": {
        "policy number": "string",
        "Previous policy number/Renewal of Policy": "string",
        "Named Insured": "string",
        "Policy Period": "FROM YYYY-MM-DD TO YYYY-MM-DD",
        "Issuing Company/ Issuing Carrier/Insurer": "string",
        "Broker/Surplus Broker": "string",
        "Description of Business or Premises": "string",
        "All Premises You Own, Rent, or Occupy": "string",
        "Schedule of Forms and Endorsements": "dict array",
        "Schedule of Locations": "array of strings",
        "Written Premium (Premium before TRIA, Taxes, and Fees)": "float",
        "Terrorism (Included, Not covered, Rejected, or numeric value $)": "string or float",
        "Total Taxes": "float",
        "Total Fees & Surcharges": "float",
        "Each Occurrence Limit/ POLICY LIMIT": "float",
        "Damage To Premises Rented To You Limit": "float",
        "Medical Expense Limit / Medical Payments": "float",
        "Personal & Advertising Injury Aggregate Limit": "float",
        "General Aggregate Limit": "float",
        "Products/Completed Operations Aggregate Limit": "float",
        "Deductible": "float"
    },

    "Applied United Risk Logistics": {
        "policy number": "string",
        "Previous policy number/Renewal of Policy": "string",
        "Named Insured": "string",
        "Address/Mailing Address": "string",
        "Policy Period": "FROM YYYY-MM-DD TO YYYY-MM-DD",
        "Issuing Company/ Issuing Carrier/Insurer": "string",
        "Broker/Surplus Broker": "string"
    },
    "commodities": {
        "Subjectivities &/or Warranties ( list of conditions)": "array of strings",
        "Commodity Terms & Conditions ( Commodity, Sublimit, Deductible)": "array of objects",
        "Commodity Details": [
            {
                "Commodity": "string",
                "Sublimit": "float",
                "Deductible": "float"
            }
        ],
        "Rate Schedule ( rates for different commodity groups and Warehouse)": "array of objects",
        "Rate Details": [
            {
                "Commodity Group": "string",
                "Rate": "float",
                "Warehouse": "string"
            }
        ],
        "General Terms & Conditions (lists of forms and endorsements titles)": "array of strings"
    },
    "premiums_and_fees": {
        "Cargo Deposit Premium": "float",
        "Warehouse Deposit Premium": "float",
        "Terrorism (Included, Not covered, Rejected, or numeric value $)": "string or float",
        "Total Taxes": "float",
        "Total Fees & Surcharges": "float",
        "Ocean Cargo & Inland Transit ( Limit and Deductible)": {
            "Limit": "float",
            "Deductible": "float"
        },
        "Warehouse Storage( Limit and Deductible, CAT Sublimit and Deductible)": {
            "Limit": "float",
            "Deductible": "float",
            "CAT Sublimit": "float",
            "CAT Deductible": "float"
        }

    },

    "Applied Financial Lines Excess Liability": {
        "policy number": "string",
        "Previous policy number/Renewal of Policy": "string",
        "Named Insured": "string",
        "Address/Mailing Address": "string",
        "Policy Period": "FROM YYYY-MM-DD TO YYYY-MM-DD",
        "Issuing Company/ Issuing Carrier/Insurer": "string",
        "Schedule of Forms and Endorsements": "dict array including all fields",
        "Total Annual Premium (including Terrorism, Tax, and Fees)": "float",
        "Each Occurrence Limit/ POLICY LIMIT": "float",
        "General Aggregate Limit": "float",
        "Carrier/ Controlling Underlying Carrier": "string",
        "policy number/ Controlling Underlying Policy": "string",
        "Limits": "float"
    },

    "Applied Financial Lines Professional Liability": {

        "policy number": "string",
        "Previous policy number/Renewal of Policy": "string",
        "Named Insured": "string",
        "Address/Mailing Address": "string",
        "Policy Period": "FROM YYYY-MM-DD TO YYYY-MM-DD",
        "Issuing Company/ Issuing Carrier/Insurer": "string",
        "Retroactive Date": "YYYY-MM-DD",
        "Schedule of Forms and Endorsements": "dict array",
        "Total Annual Premium (including Terrorism, Tax, and Fees)": "annual premium",
        "Minimum Premium": "float",
        "Each Occurrence Limit/ POLICY LIMIT": "float",
        "Each Claim Expense": "float",
        "General Aggregate Limit": "float",
        "Deductible": "float"
    },

    "Applied Financial Lines Management Liability": {
        "policy number": "string",
        "Previous policy number/Renewal of Policy": "string",
        "Named Insured": "string",
        "Address/Mailing Address": "string",
        "Producer Name/ Producing Agency": "string",
        "Policy Period": "FROM YYYY-MM-DD TO YYYY-MM-DD",
        "Schedule of Forms and Endorsements": "array dictionary (Ensure Correct values for each header)",
        "Optional Extension Period": "string",
        "Pending and Prior Litigation Date": "YYYY-MM-DD",
        "Total Annual Premium (including Terrorism, Tax, and Fees)": "float",
        "Aggregate Limit of Liability for the Policy Period for all Investigation Demands": "float",
        "Aggregate Limit of Liability for the Policy Period, including Defense Costs, for all Loss from all Claims, Insured Person Inquiries, and Investigation Demands": "float",
        "Each Claim under Insuring Agreement (A) or (D)": "float",
        "Each Claim, other than a Securities Claim, under Insuring Agreement (B) or E": "float",
        "Each Securities Claim under Insuring Agreement (C)": "float",
        "Each Investigation Demand under Insuring Agreement (F)": "float"

    },
    #DONE
    "Rivington Partners Commercial Property": {
        "policy number": "string",
        "Named Insured": "string",
        "Address/Mailing Address": "string",
        "Producer Name/ Producing Agency": "string",
        "Policy Period": "FROM YYYY-MM-DD TO YYYY-MM-DD",
        "Issuing Company/ Issuing Carrier/Insurer": "string",
        "Description of Business or Premises": "string (Business or Premises, pick any or both if present)",
        "Co-Insurance": "string",
        "Insured Values": "string",
        "Perils Insured": "string",
        "Territory": "string",
        "Valuation": "string",
        "Sub-Limits": "float",
        "Schedule of Forms and Endorsements": "dict array",
        "Written Premium (Premium before TRIA, Taxes, and Fees)": "float (total premium)",
        "Terrorism (Included, Not covered, Rejected, or numeric value $)": "string or float",
        "Total Taxes": "string or float",
        "Total Fees & Surcharges": "string or float (If multiple Fees present, mention all of them)",
        "Each Occurrence Limit/ POLICY LIMIT": "string or float",
        "Controlling Underlying Carrier": "string",
        "Controlling Underlying Policy": "string"
    }
}
