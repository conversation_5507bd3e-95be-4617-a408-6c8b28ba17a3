import concurrent.futures
import base64
import concurrent.futures
import concurrent.futures
import io
import os
from typing import List, Optional, Literal

import fitz  # PyMuPDF
import pdfplumber
import pytesseract
from PIL import Image
from PyPDF2 import PdfReader, PdfWriter
from langchain_core.documents import Document
from mistralai import Mistral
from pdf2image import convert_from_path

from utils.LogUtils import logger


class PDFOCRReader:
    def __init__(self,
                 extractor_type: Literal["fitz", "pdfplumber", "pytesseract", "mistral", "compare"] = "compare",
                 tesseract_path: Optional[str] = None,
                 poppler_path: Optional[str] = None):
        if tesseract_path:
            pytesseract.pytesseract.tesseract_cmd = tesseract_path
        self.poppler_path = poppler_path
        self.extractor_type = extractor_type

    def pdf_to_images(self, pdf_path: str, dpi: int = 300) -> List[Image.Image]:
        return convert_from_path(pdf_path, dpi=dpi, poppler_path=self.poppler_path)

    def image_to_text(self, image: Image.Image, config: str = r'--psm 3') -> str:
        return pytesseract.image_to_string(image, config=config).strip()

    def extract_text_with_fitz(self, file_path: str) -> List[Document]:
        documents = []
        try:
            doc = fitz.open(file_path)
            for index, page in enumerate(doc):
                text = page.get_text()
                if text.strip():
                    documents.append(Document(
                        page_content=text,
                        metadata={"source": os.path.basename(file_path), "page_number": index + 1, "file_path": file_path}
                    ))
            doc.close()
            logger.info(f"Extracted {len(documents)} pages using fitz from {os.path.basename(file_path)}")
        except Exception as e:
            logger.error(f"Error reading PDF with fitz: {e}")
        return documents

    def extract_text_with_pdfplumber(self, file_path: str) -> List[Document]:
        documents = []
        try:
            with pdfplumber.open(file_path) as pdf:
                for index, page in enumerate(pdf.pages):
                    text = page.extract_text()
                    if text and text.strip():
                        documents.append(Document(
                            page_content=text,
                            metadata={"source": os.path.basename(file_path), "page_number": index + 1, "file_path": file_path}
                        ))
            logger.info(f"Extracted {len(documents)} pages using pdfplumber from {os.path.basename(file_path)}")
        except Exception as e:
            logger.error(f"Error reading PDF with pdfplumber: {e}")
        return documents

    def extract_text_with_pytesseract(self, file_path: str, max_pages: Optional[int] = None) -> List[Document]:
        documents = []
        try:
            images = self.pdf_to_images(file_path)
            if max_pages:
                images = images[:max_pages]

            def process_page(i_img_tuple):
                i, image = i_img_tuple
                try:
                    text = self.image_to_text(image)
                    return Document(
                        page_content=text,
                        metadata={"source": os.path.basename(file_path), "page_number": i + 1, "file_path": file_path}
                    )
                except Exception as e:
                    logger.error(f"OCR failed on page {i + 1}: {e}")
                    return None

            with concurrent.futures.ThreadPoolExecutor() as executor:
                results = list(executor.map(process_page, enumerate(images)))

            documents = [doc for doc in results if doc is not None]
            logger.info(f"OCR processed {len(documents)} pages from '{file_path}' using Tesseract")
        except Exception as e:
            logger.error(f"OCR extraction failed: {e}")
        return documents

    def extract_text_with_mistral(self, file_path: str) -> List[Document]:
        def encode_pdf(pdf_path):
            try:
                with open(pdf_path, "rb") as pdf_file:
                    return base64.b64encode(pdf_file.read()).decode('utf-8')
            except Exception as e:
                logger.error(f"PDF encoding error: {e}")
                return None

        base64_pdf = encode_pdf(file_path)
        if base64_pdf is None:
            return []

        try:
            client = Mistral(api_key=os.environ["MISTRAL_API_KEY"])
            ocr_response = client.ocr.process(
                model="mistral-ocr-latest",
                document={
                    "type": "document_url",
                    "document_url": f"data:application/pdf;base64,{base64_pdf}"
                },
                include_image_base64=True
            )

            documents = []
            for i, page in enumerate(ocr_response.pages):
                documents.append(Document(
                    page_content=page.markdown,
                    metadata={
                        "source": file_path,
                        "page_number": i + 1,
                        "mistral_page_index": page.index,
                        "file_path": file_path
                    }
                ))
            return documents

        except Exception as e:
            logger.error(f"Mistral OCR failed: {e}")
            return []

    def compare_and_choose_best(self, file_path: str, max_pages: Optional[int] = None) -> List[Document]:
        try:
            logger.info("Comparing all extractors page-by-page.")
            methods = {
                "fitz": self.extract_text_with_fitz(file_path),
                "pdfplumber": self.extract_text_with_pdfplumber(file_path),
                "pytesseract": self.extract_text_with_pytesseract(file_path, max_pages),
            }

            pages_by_method = {}
            for method_name, docs in methods.items():
                for doc in docs:
                    page_number = doc.metadata["page_number"]
                    if page_number not in pages_by_method:
                        pages_by_method[page_number] = {}
                    pages_by_method[page_number][method_name] = doc

            final_docs = []
            for page_number in sorted(pages_by_method.keys()):
                page_versions = pages_by_method[page_number]
                best_method = max(page_versions.items(), key=lambda x: len(x[1].page_content))[0]
                final_docs.append(page_versions[best_method])

            logger.info(f"Selected best method per page for {file_path}")
            return final_docs

        except Exception as e:
            logger.error(f"[Comparison] Error during extractor comparison: {e}")
            return []

    def load_pdf_documents(self, file_path: str, max_pages: Optional[int] = None) -> List[Document]:
        if not os.path.exists(file_path):
            logger.error(f"File not found: {file_path}")
            return []

        method_dispatch = {
            "fitz": self.extract_text_with_fitz,
            "pdfplumber": self.extract_text_with_pdfplumber,
            "pytesseract": lambda f: self.extract_text_with_pytesseract(f, max_pages),
            "mistral": self.extract_text_with_mistral,
            "compare": lambda f: self.compare_and_choose_best(f, max_pages)
        }

        extractor = method_dispatch.get(self.extractor_type)
        if extractor:
            return extractor(file_path)
        else:
            logger.error(f"Invalid extractor type: {self.extractor_type}")
            return []


def extract_pdf_page(pdf_path, page_number, output_folder=None):
    reader = PdfReader(pdf_path)
    total_pages = len(reader.pages)

    if page_number < 1 or page_number > total_pages:
        raise ValueError(f"Page number must be between 1 and {total_pages}")

    writer = PdfWriter()
    writer.add_page(reader.pages[page_number - 1])

    if output_folder is None:
        output_folder = os.path.dirname(os.path.abspath(pdf_path))

    output_path = os.path.join(output_folder, f"page_{page_number}.pdf")
    with open(output_path, "wb") as f:
        writer.write(f)

    return output_path


def extract_text_pdfplumber(pdf_path, page_number):
    with pdfplumber.open(pdf_path) as pdf:
        for i, page in enumerate(pdf.pages):
            if i == page_number - 1:
                text = page.extract_text()
                return text
        return None


def pdf_to_images_base64(pdf_path, page_number=1):
    """Convert specific PDF page to base64 encoded image"""
    images = convert_from_path(pdf_path)

    # Check if page exists
    if page_number < 1 or page_number > len(images):
        logger.error(f"Page {page_number} not found. PDF has {len(images)} pages.")
        return None

    # Get specific page (convert 1-indexed to 0-indexed)
    image = images[page_number - 1]

    buffered = io.BytesIO()
    image.save(buffered, format="PNG")
    img_base64 = base64.b64encode(buffered.getvalue()).decode()

    return img_base64
