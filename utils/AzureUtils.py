# | Function                  | Purpose                            |
# | ------------------------- | ---------------------------------- |
# | `list_files_in_folder()`  | Lists files in a virtual folder    |
# | `download_blob()`         | Downloads a single file            |
# | `download_folder()`       | Downloads an entire folder         |
# | `upload_file_to_folder()` | Uploads a single file to a folder  |
# | `upload_folder()`         | Uploads a local folder recursively |


from azure.storage.blob import BlobServiceClient, ContainerClient, BlobClient
from Constants.Config import settings
import os

AZURE_CONNECTION_STRING = settings.azure_connection_string
CONTAINER_NAME = settings.azure_container

blob_service_client = BlobServiceClient.from_connection_string(AZURE_CONNECTION_STRING)
container_client = blob_service_client.get_container_client(CONTAINER_NAME)


def list_files_in_folder(folder_path: str):
    """
    Lists all blobs (files) under a given virtual folder path in the container.
    """
    blob_list = container_client.list_blobs(name_starts_with=folder_path)
    return [blob.name for blob in blob_list]


def download_blob(blob_path: str, download_dir: str):
    """
    Downloads a single blob to a local directory.
    """
    blob_client = container_client.get_blob_client(blob_path)
    local_path = os.path.join(download_dir, os.path.basename(blob_path))
    os.makedirs(download_dir, exist_ok=True)

    with open(local_path, "wb") as f:
        data = blob_client.download_blob()
        f.write(data.readall())

    return local_path


def download_folder(folder_path: str, download_dir: str):
    """
    Downloads all blobs under a virtual folder to a local directory.
    """
    blobs = list_files_in_folder(folder_path)
    downloaded_files = []

    for blob_path in blobs:
        relative_path = os.path.relpath(blob_path, folder_path)
        local_path = os.path.join(download_dir, relative_path)
        os.makedirs(os.path.dirname(local_path), exist_ok=True)

        blob_client = container_client.get_blob_client(blob_path)
        with open(local_path, "wb") as f:
            f.write(blob_client.download_blob().readall())
        downloaded_files.append(local_path)

    return downloaded_files


def upload_file_to_folder(local_file_path: str, target_folder: str):
    """
    Uploads a single file to a virtual folder in Azure Blob Storage.
    """
    file_name = os.path.basename(local_file_path)
    blob_path = os.path.join(target_folder, file_name).replace("\\", "/")

    blob_client = container_client.get_blob_client(blob_path)
    with open(local_file_path, "rb") as data:
        blob_client.upload_blob(data, overwrite=True)

    return blob_path


def upload_folder(local_folder_path: str, target_folder: str):
    """
    Uploads all files from a local folder to a virtual folder in Azure.
    """
    uploaded_blobs = []

    for root, _, files in os.walk(local_folder_path):
        for file in files:
            full_path = os.path.join(root, file)
            relative_path = os.path.relpath(full_path, local_folder_path)
            blob_path = os.path.join(target_folder, relative_path).replace("\\", "/")

            blob_client = container_client.get_blob_client(blob_path)
            with open(full_path, "rb") as data:
                blob_client.upload_blob(data, overwrite=True)

            uploaded_blobs.append(blob_path)

    return uploaded_blobs
