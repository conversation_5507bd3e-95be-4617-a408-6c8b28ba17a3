import difflib
import json
import os
from pathlib import Path
from typing import List

import tiktoken
from langchain_core.documents import Document
from langchain_core.tools import tool
from openpyxl import load_workbook
from openpyxl.styles import <PERSON>ont, PatternFill, Border, Side
from openpyxl.utils import get_column_letter

from Constants.PolicyFields import POLICY_FIELD_MAPPINGS
from utils.LogUtils import logger

enc = tiktoken.encoding_for_model("gpt-4o")


def list_pdfs(directory):
    pdf_files = []
    for root, _, files in os.walk(directory):
        for file in files:
            if file.lower().endswith(".pdf"):
                pdf_files.append(os.path.join(root, file))
    return pdf_files


def limit_chunks_by_max_tokens(chunks: List[Document], max_tokens: int, model_name: str = "gpt-4o") -> List[Document]:
    """
    Select chunks from the input list without exceeding max_tokens.
    Uses tiktoken to count tokens. Stops when the limit is reached.
    No truncation or scoring involved.
    """
    selected_chunks = []
    total_tokens = 0

    for chunk in chunks:
        chunk_tokens = len(enc.encode(chunk.page_content))
        if total_tokens + chunk_tokens <= max_tokens:
            selected_chunks.append(chunk)
            total_tokens += chunk_tokens
        else:
            break

    return selected_chunks


def format_to_json(resp):
    """
    Convert a response to JSON format with robust error handling.

    Args:
        resp (Union[str, dict]): The response to convert to JSON

    Returns:
        tuple: (parsed_json, success_flag)
    """
    if isinstance(resp, dict):
        return resp, True

    # Try direct JSON parsing
    try:
        return json.loads(resp), True
    except (json.JSONDecodeError, TypeError):
        pass

    # Try cleaning and then parsing
    if isinstance(resp, str):
        try:
            # Extract JSON content starting from first '{' and strip common wrappers
            resp = resp[resp.find('{'):] if '{' in resp else resp
            for tag in ("```json", "```plaintext", "```"):
                resp = resp.replace(tag, "")
            resp = resp.replace("None", "null")

            return json.loads(resp), True
        except Exception as e:
            logger.error(f"Unable to convert response into JSON: {e}")

    return resp, False


def convert_paths_to_strings(obj):
    """
    Recursively convert PosixPath objects to strings for JSON serialization.
    """
    if isinstance(obj, Path):
        return str(obj)
    elif isinstance(obj, dict):
        return {str(k) if isinstance(k, Path) else k: convert_paths_to_strings(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_paths_to_strings(item) for item in obj]
    elif isinstance(obj, tuple):
        return tuple(convert_paths_to_strings(item) for item in obj)
    else:
        return obj


def clear_folder(folder_path):
    for filename in os.listdir(folder_path):
        file_path = os.path.join(folder_path, filename)
        try:
            if os.path.isfile(file_path):
                os.remove(file_path)
        except Exception as e:
            print(f"Error deleting {file_path}: {e}")


@tool
def get_schema(policy_name : str) -> str | None:
    """ Function to get relevant schema based on Policy type"""
    best_match = None
    best_similarity = 0.0
    similarity_threshold = 0.7
    for policy_type in POLICY_FIELD_MAPPINGS.keys():
        similarity = difflib.SequenceMatcher(None, policy_name.strip().lower(), policy_type.lower()).ratio()
        if similarity > best_similarity:
            best_similarity = similarity
            best_match = policy_type
        fields_to_extract = POLICY_FIELD_MAPPINGS[best_match]
        return str(fields_to_extract)
    return None


def get_leaf_key_value_pairs(d):
    leaf_pairs = {}

    def recurse(current, parent_key=""):
        if isinstance(current, dict):
            for k, v in current.items():
                recurse(v, k)
        else:
            leaf_pairs[parent_key] = current

    recurse(d)
    return leaf_pairs


def format_output_excel(output_path):
    try:
        wb = load_workbook(output_path)

        # Define styles
        header_fill = PatternFill(start_color="ADD8E6", end_color="ADD8E6", fill_type="solid")  # Light blue
        bold_font = Font(bold=True)
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

        for ws in wb.worksheets:  # Loop over all sheets
            # Format header row
            for col_num, cell in enumerate(ws[1], 1):
                cell.fill = header_fill
                cell.font = bold_font
                cell.border = thin_border
                col_letter = get_column_letter(col_num)
                ws.column_dimensions[col_letter].auto_size = True

            # Format all other data rows
            for row in ws.iter_rows(min_row=2, max_row=ws.max_row, min_col=1, max_col=ws.max_column):
                for cell in row:
                    cell.border = thin_border

            # Auto-adjust column widths and wrap text
            for col in ws.columns:
                max_length = 0
                col_letter = get_column_letter(col[0].column)
                for cell in col:
                    try:
                        if cell.value:
                            max_length = max(max_length, len(str(cell.value)))
                    except:
                        pass
                max_allowed_width = 50
                ws.column_dimensions[col_letter].width = min(max_length + 4, max_allowed_width)

                for cell in col:
                    cell.alignment = cell.alignment.copy(wrap_text=True)

        # Save the styled workbook
        wb.save(output_path)

    except Exception as e:
        logger.error(f"Error formatting output Excel: {e}")
