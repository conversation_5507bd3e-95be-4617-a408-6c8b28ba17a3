# import logging
# from rich.logging import <PERSON>Hand<PERSON>
#
# # Basic config for logging
# logging.basicConfig(
#     level=logging.DEBUG,  # or INFO, WARNING
#     format="%(asctime)s %(name)s [%(levelname)s] %(message)s",
#     datefmt="[%Y-%m-%d %H:%M:%S]",
#     handlers=[RichHandler(rich_tracebacks=True)]
# )
#
# logger = logging.getLogger("myapp")

import os
import sys
from loguru import logger

#from constants.app_constant import ENV_LEVEL

ENV = os.getenv("ENV","LOCAL")
# Customize the logger format and retain color scheme
logger.remove()  # Remove default configuration
logger.add(
    sys.stdout,
    format="<red>{time:YYYY-MM-DD HH:mm:ss}</red> | "  # Timestamp
           "<cyan>{extra[env]}</cyan> | "  # Environment variable
           "<level>{level: <5}</level> | "  # Log level
           "<green>{message}</green>",  # Log message
    colorize=True  # Enable colored output
)

logger = logger.bind(env=ENV)


class cs_logger():
    def debug(self, msg, *args, **kwargs):
        logger.debug(msg, *args, **kwargs)

    def info(self, msg, *args, **kwargs):
        logger.info(msg, *args, **kwargs)

    def warning(self, msg, *args, **kwargs):
        logger.warning(msg, *args, **kwargs)

    def error(self, msg, *args, **kwargs):
        logger.error(msg, *args, **kwargs)

    def critical(self, msg, *args, **kwargs):
        logger.critical(msg, *args, **kwargs)

    def exception(self, msg, *args, **kwargs):
        logger.exception(msg, *args, **kwargs)

    def catch(self, msg, *args, **kwargs):
        logger.catch(msg, *args, **kwargs)

    def success(self, msg, *args, **kwargs):
        logger.success(msg, *args, **kwargs)

    def err(self, msg, *args, **kwargs):
        logger.error(msg, *args, **kwargs)

    def warn(self, msg, *args, **kwargs):
        logger.warning(msg, *args, **kwargs)

    def trace(self, msg, *args, **kwargs):
        logger.trace(msg, *args, **kwargs)

