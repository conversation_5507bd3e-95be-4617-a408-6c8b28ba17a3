"""
<PERSON>ript to run the Streamlit Insurance Policy Scrapper application.
"""

import subprocess
import sys
import os
from pathlib import Path


def check_streamlit_installed():
    """Check if Streamlit is installed"""
    try:
        import streamlit
        return True
    except ImportError:
        return False


def install_requirements():
    """Install requirements if needed"""
    print("Installing requirements...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Requirements installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing requirements: {e}")
        return False


def run_streamlit():
    """Run the Streamlit application"""
    print("🚀 Starting Streamlit Insurance Policy Scrapper...")
    print("📱 The app will open in your default web browser")
    print("🔗 URL: http://localhost:8501")
    print("⏹️  Press Ctrl+C to stop the server")
    print("-" * 50)

    try:
        # Run streamlit with the app file
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "streamlit_show.py",
            "--server.port", "8501",
            "--server.address", "localhost",
            "--browser.gatherUsageStats", "false"
        ])
    except KeyboardInterrupt:
        print("\n👋 Streamlit app stopped by user")
    except Exception as e:
        print(f"❌ Error running Streamlit: {e}")


def main():
    """Main function"""
    print("🛡️📄 Insurance Policy Scrapper - Streamlit Runner")
    print("=" * 50)

    # Check if we're in the right directory
    if not Path("streamlit_show.py").exists():
        print("❌ Error: streamlit_show.py not found in current directory")
        print("Please run this script from the project root directory")
        sys.exit(1)

    # Check if Streamlit is installed
    if not check_streamlit_installed():
        print("⚠️  Streamlit not found. Installing requirements...")
        if not install_requirements():
            print("❌ Failed to install requirements. Please install manually:")
            print("pip install -r requirements.txt")
            sys.exit(1)

    # Run the app
    run_streamlit()


if __name__ == "__main__":
    main()
