import streamlit as st
import pandas as pd
import os
import time
import json
from typing import Optional
from datetime import datetime

from Constants.Config import settings
from InfoExtractor.policy_scrapper import process_policy_docs
from utils.LogUtils import logger
from utils.DataUtils import clear_folder


# --- Page Configuration ---
st.set_page_config(
    page_title="Insurance Policy Scrapper | AI-Powered Document Analysis",
    page_icon="🛡️",
    layout="wide",
    initial_sidebar_state="collapsed",
    menu_items={
        'Get Help': 'https://github.com/your-repo/help',
        'Report a bug': 'https://github.com/your-repo/issues',
        'About': "# Insurance Policy Scrapper\nAI-powered document analysis for insurance policies."
    }
)


# --- Session State Initialization ---
def initialize_session_state():
    """Initialize all session state variables"""
    session_vars = {
        'pdf_uploaded': False,
        'excel_path': None,
        'pdf_path': None,
        'processing': False,
        'processing_complete': False,
        'error_message': None,
        'json_result': None,
        'upload_success': False,
        'processing_start_time': None,
        'processing_history': [],
        'processing_stats': {},
        'show_reset_confirmation': False,
        'reset_in_progress': False
    }

    for var, default_value in session_vars.items():
        if var not in st.session_state:
            st.session_state[var] = default_value

initialize_session_state()


# --- Utility Functions ---
def get_file_size_mb(file_size_bytes):
    """Convert bytes to MB with formatting"""
    return round(file_size_bytes / (1024 * 1024), 2)


def format_processing_time(seconds):
    """Format processing time in a human-readable way"""
    if seconds < 60:
        return f"{seconds:.1f} seconds"
    elif seconds < 3600:
        minutes = seconds // 60
        remaining_seconds = seconds % 60
        return f"{int(minutes)}m {remaining_seconds:.0f}s"
    else:
        hours = seconds // 3600
        remaining_minutes = (seconds % 3600) // 60
        return f"{int(hours)}h {int(remaining_minutes)}m"


def add_to_processing_history(filename, status, processing_time=None, error=None):
    """Add entry to processing history"""
    entry = {
        'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        'filename': filename,
        'status': status,
        'processing_time': processing_time,
        'error': error
    }

    if 'processing_history' not in st.session_state:
        st.session_state.processing_history = []

    st.session_state.processing_history.insert(0, entry)  # Add to beginning

    # Keep only last 10 entries
    if len(st.session_state.processing_history) > 10:
        st.session_state.processing_history = st.session_state.processing_history[:10]


# --- Enhanced Helper Functions ---
def validate_pdf_file(uploaded_file) -> tuple[bool, str, dict]:
    """Validate uploaded PDF file with detailed information"""
    if uploaded_file is None:
        return False, "No file uploaded", {}

    file_info = {
        'name': uploaded_file.name,
        'size_bytes': uploaded_file.size,
        'size_mb': get_file_size_mb(uploaded_file.size),
        'type': uploaded_file.type
    }

    if not uploaded_file.name.lower().endswith('.pdf'):
        return False, "❌ Please upload a PDF file only", file_info

    if uploaded_file.size > 50 * 1024 * 1024:  # 50MB limit
        return False, f"❌ File size too large ({file_info['size_mb']} MB). Please upload a file smaller than 50MB", file_info

    if uploaded_file.size < 1024:  # Less than 1KB
        return False, "❌ File appears to be too small or corrupted", file_info

    return True, f"✅ File is valid ({file_info['size_mb']} MB)", file_info




def save_uploaded_file(uploaded_file) -> tuple[bool, str, Optional[str]]:
    """Save uploaded file to the designated folder with enhanced feedback"""
    try:
        # Ensure the root folder exists
        os.makedirs(settings.root_folder, exist_ok=True)

        # Clear old files from the folder before uploading new one
        try:
            logger.info(f"Clearing old files from folder: {settings.root_folder}")
            clear_folder(settings.root_folder)
            logger.info("Old files cleared successfully")
        except Exception as clear_error:
            logger.warning(f"Warning: Could not clear old files: {clear_error}")
            # Continue with upload even if clearing fails

        filename = uploaded_file.name
        save_path = os.path.join(settings.root_folder, filename)

        # Safety check for existing files (though folder was cleared)
        counter = 1
        original_path = save_path
        while os.path.exists(save_path):
            name, ext = os.path.splitext(original_path)
            save_path = f"{name}_{counter}{ext}"
            counter += 1

        # Reset file pointer to beginning
        uploaded_file.seek(0)

        with open(save_path, "wb") as f:
            f.write(uploaded_file.read())

        logger.info(f"File saved successfully: {save_path}")
        return True, f"✅ File saved as: {os.path.basename(save_path)}", save_path

    except Exception as e:
        logger.error(f"Error saving file: {e}")
        return False, f"❌ Error saving file: {str(e)}", None




def reset_session():
    """Reset all session state variables and clear old files"""
    st.session_state.reset_in_progress = True

    # Clear old files from the folder
    try:
        if os.path.exists(settings.root_folder):
            logger.info(f"Clearing files from folder during session reset: {settings.root_folder}")
            clear_folder(settings.root_folder)
            logger.info("Files cleared successfully during session reset")
    except Exception as clear_error:
        logger.warning(f"Warning: Could not clear files during session reset: {clear_error}")
        st.session_state.error_message = f"Warning: Could not clear old files: {clear_error}"

    # Reset session state variables
    st.session_state.pdf_uploaded = False
    st.session_state.excel_path = None
    st.session_state.pdf_path = None
    st.session_state.processing = False
    st.session_state.processing_complete = False
    st.session_state.error_message = None
    st.session_state.json_result = None
    st.session_state.upload_success = False
    st.session_state.processing_start_time = None
    st.session_state.processing_stats = {}
    st.session_state.show_reset_confirmation = False
    st.session_state.reset_in_progress = False







# --- Main Application ---
def main():
    """Enhanced main application function"""

    # --- Header with enhanced styling ---
    st.markdown("""
    <div style="text-align: center; margin-bottom: 2rem;">
        <h1 class="main-header">🛡️ Insurance Policy Scrapper</h1>
        <p style="font-size: 1.2rem; color: #64748b; margin-top: -1rem;">
            AI-Powered Document Data Extraction
        </p>
        <div style="width: 100px; height: 4px; background: linear-gradient(90deg, #667eea 0%, #764ba2 100%); margin: 1rem auto; border-radius: 2px;"></div>
    </div>
    """, unsafe_allow_html=True)



    # --- Error Display ---
    if st.session_state.error_message:
        st.error(st.session_state.error_message)

        # Add error recovery suggestions
        st.markdown("#### 🔧 Troubleshooting Tips")
        st.info("""
        - Ensure your PDF file is not corrupted
        - Check that the file size is under 50MB
        - Try uploading a different PDF file
        - Refresh the page and try again
        """)

    # --- Upload Section ---
    st.markdown("### 📁 Upload Policy Document")

    st.markdown("*Drag and drop your PDF file or click to browse*")
    uploaded_file = st.file_uploader(
        "Choose a PDF file",
        type="pdf",
        help="Upload your insurance policy document in PDF format (Max: 50MB)",
        label_visibility="collapsed"
    )

    # Handle file upload
    if uploaded_file:
        # Enhanced validation with file info
        is_valid, message, file_info = validate_pdf_file(uploaded_file)

        if is_valid:
            if not st.session_state.upload_success:
                # Save file with progress indication
                with st.spinner("💾 Saving file..."):
                    success, save_message, save_path = save_uploaded_file(uploaded_file)

                if success:
                    st.session_state.pdf_path = save_path
                    st.session_state.pdf_uploaded = True
                    st.session_state.upload_success = True
                    st.session_state.error_message = None
                    st.success(save_message)
                else:
                    st.session_state.error_message = save_message
                    st.rerun()
            else:
                st.success(f"✅ File ready: {uploaded_file.name}")
        else:
            st.session_state.error_message = message
            st.rerun()



    # --- Enhanced Processing Section ---
    if st.session_state.pdf_uploaded and not st.session_state.processing_complete:
        st.markdown("---")
        st.markdown("### 🔄 Document Processing")

        col1, col2, col3 = st.columns([1, 2, 1])

        with col2:
            if not st.session_state.processing:
                # Enhanced process button with confirmation
                button_text = "🚀 Start Processing"
                help_text = "Begin extracting data from your policy document"

                if st.button(button_text, type="primary", use_container_width=True, help=help_text):
                    st.session_state.processing = True
                    st.session_state.processing_start_time = time.time()
                    st.session_state.error_message = None
                    st.rerun()
            else:
                # Single file processing
                st.info("⏳ Processing document... Please wait.")

                # Enhanced progress tracking
                progress_container = st.container()
                with progress_container:
                    progress_bar = st.progress(0)
                    status_text = st.empty()
                    time_text = st.empty()

                try:
                    start_time = st.session_state.processing_start_time or time.time()
                    result = process_policy_docs(settings.root_folder, False)

                    # Calculate processing time
                    processing_time = time.time() - start_time

                    # Update session state with results and stats
                    st.session_state.json_result = result['result']
                    st.session_state.excel_path = result['output_file_path']
                    st.session_state.processing = False
                    st.session_state.processing_complete = True

                    # Store processing statistics
                    st.session_state.processing_stats = {
                        'processing_time': format_processing_time(processing_time),
                        'fields_extracted': len(result.get('result', {})) if result.get('result') else 0
                    }

                    # Add to processing history
                    filename = os.path.basename(st.session_state.pdf_path) if st.session_state.pdf_path else 'Unknown'
                    add_to_processing_history(filename, 'success', format_processing_time(processing_time))

                    progress_bar.progress(100)
                    status_text.text("✅ Processing complete!")
                    time_text.text(f"⏱️ Total time: {format_processing_time(processing_time)}")

                    st.success(f"🎉 Document processed successfully in {format_processing_time(processing_time)}!")
                    st.rerun()

                except Exception as e:
                    processing_time = time.time() - start_time if st.session_state.processing_start_time else 0
                    logger.error(f"Processing error: {e}")

                    # Add to processing history
                    filename = os.path.basename(st.session_state.pdf_path) if st.session_state.pdf_path else 'Unknown'
                    add_to_processing_history(filename, 'error', format_processing_time(processing_time), str(e))

                    st.session_state.error_message = f"❌ Processing failed: {str(e)}"
                    st.session_state.processing = False
                    st.rerun()

    # --- Enhanced Results Section ---
    if st.session_state.processing_complete and st.session_state.excel_path:
        st.markdown("---")
        # Single file results
        st.markdown("### 📊 Extraction Results")

        try:
            # Load and display Excel data
            df = pd.read_excel(st.session_state.excel_path)

            # Enhanced metrics dashboard
            st.markdown("#### 📈 Processing Summary")
            col1, col2, col3 = st.columns(3)

            with col1:
                st.metric(
                    "📋 Fields Extracted",
                    len(df),
                    help="Total number of data fields extracted from the document"
                )
            with col2:
                st.metric(
                    "⏱️ Processing Time",
                    st.session_state.processing_stats.get('processing_time', 'N/A'),
                    help="Time taken to process the document"
                )
            with col3:
                st.metric(
                    "📄 Document Status",
                    "✅ Processed",
                    help="Current status of the document processing"
                )

            # Enhanced data display with filtering
            st.markdown("#### 📋 Extracted Data")

            # Add search and filter options
            col1, col2 = st.columns([2, 1])
            with col1:
                search_term = st.text_input("🔍 Search fields", placeholder="Type to search...")

            # Filter dataframe based on search and empty field settings
            display_df = df.copy()
            if search_term:
                mask = display_df.iloc[:, 0].str.contains(search_term, case=False, na=False)
                display_df = display_df[mask]

            # Display filtered data with enhanced styling
            st.dataframe(
                display_df,
                use_container_width=True,
                hide_index=True,
                column_config={
                    display_df.columns[0]: st.column_config.TextColumn("Field Name", width="medium"),
                    display_df.columns[1]: st.column_config.TextColumn("Extracted Value", width="large")
                }
            )

            # Data export section
            st.markdown("#### 💾 Download & Export Options")

            col1, col2 = st.columns(2)

            with col1:
                # Excel download with timestamp
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                with open(st.session_state.excel_path, "rb") as file:
                    st.download_button(
                        label="📥 Download Excel",
                        data=file.read(),
                        file_name=f"policy_extraction_{timestamp}.xlsx",
                        mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                        type="primary",
                        help="Download results as Excel file"
                    )

            with col2:
                # JSON download
                if st.session_state.json_result:
                    json_str = json.dumps(st.session_state.json_result, indent=2)
                    st.download_button(
                        label="📥 Download JSON",
                        data=json_str,
                        file_name=f"policy_extraction_{timestamp}.json",
                        mime="application/json",
                        help="Download raw data as JSON file"
                    )

        except Exception as e:
            logger.error(f"Error displaying results: {e}")
            st.error(f"❌ Error loading results: {str(e)}")

            # Error recovery option
            if st.button("🔄 Retry Loading Results"):
                st.rerun()

    # --- Enhanced Reset Section ---
    if st.session_state.pdf_uploaded or st.session_state.processing_complete:
        st.markdown("---")
        st.markdown("### 🔄 Next Steps")

        # Show reset in progress state
        if st.session_state.reset_in_progress:
            st.info("🔄 Resetting session... Please wait.")
            return

        # Handle reset confirmation state
        if st.session_state.show_reset_confirmation:
            st.warning("⚠️ This will clear all current data. Are you sure?")

            col_a, col_b, col_c = st.columns([1, 1, 1])

            with col_a:
                if st.button("✅ Yes, Clear Session", type="primary", key="confirm_reset"):
                    with st.spinner("🔄 Clearing session..."):
                        reset_session()
                    st.success("✅ Session cleared! You can now upload a new document.")
                    time.sleep(1)
                    st.rerun()

            with col_b:
                if st.button("❌ Cancel", type="secondary", key="cancel_reset"):
                    st.session_state.show_reset_confirmation = False
                    st.rerun()
        else:
            col1, col2, col3 = st.columns([1, 2, 1])

            with col2:
                if st.button("🔄 Process New Document", type="secondary", use_container_width=True,
                            help="Clear current session and start fresh", key="process_new_doc"):
                    st.session_state.show_reset_confirmation = True
                    st.rerun()

    # --- Footer ---
    st.markdown("---")
    st.markdown("""
    <div style="text-align: center; color: #64748b; padding: 2rem 0;">
        <p>🛡️ <strong>Insurance Policy Scrapper</strong> | Powered by AI</p>
        <p style="font-size: 0.9rem;">
            Built with ❤️ using Streamlit |
            <a href="#" style="color: #667eea; text-decoration: none;">Documentation</a> |
            <a href="#" style="color: #667eea; text-decoration: none;">Support</a> |
            <a href="#" style="color: #667eea; text-decoration: none;">GitHub</a>
        </p>
    </div>
    """, unsafe_allow_html=True)


if __name__ == "__main__":
    main()
