#%%

#%%
from utils.LogUtils import logger
from OCR.Pdf_Text_Extractor import PDFOCRReader


document_path = '/Users/<USER>/Documents/CDS-6/Policy_Docs/Policy_Docs_all/Applied Entertainment Sports General Liability/BESGLPTCA011401_170564_02.pdf'
pdf_reader = PDFOCRReader(extractor_type="compare")  # or "ocr", "fitz", "pdfplumber", "compare", "mistral"
pdf_pages = pdf_reader.load_pdf_documents(str(document_path))
logger.info(f"Found {len(pdf_pages)} pages in {document_path}")
#%%
from Constants.Config import settings
from llm.SetupLLM_Embeddings import get_models

llm, embeddings = get_models(settings)

#%%
from Dal.retriever_store import init_retrievers

init_retrievers(pdf_pages, embeddings)

#%%
from Dal.VectorStoreDal import retrieve_context_hybrid, sort_and_deduplicate_chunks


query = "exclusion forms"
retrieved_pages = retrieve_context_hybrid.invoke(query,vector_weight=0.7, bm25_weight=0.3)

#%%
for doc in retrieved_pages:
    page_num = doc.metadata.get('page_number', 'Unknown')
    content = doc.page_content.strip()
    logger.info(f"[File Name {doc.metadata.get('source', 'Unknown')}, Page {page_num}]")
#%%

#%%
