import asyncio
import uuid
from concurrent.futures import Thread<PERSON>oolExecutor
from contextlib import asynccontextmanager
from pathlib import Path
from typing import Annotated, Dict, Any

import aiofiles
from fastapi import (FastAPI, BackgroundTasks, UploadFile, File, HTTPException)
from fastapi.responses import JSONResponse
from werkzeug.utils import secure_filename

from Constants.Config import settings
from InfoExtractor.policy_scrapper import process_policy_docs
from utils.DataUtils import clear_folder
from utils.LogUtils import logger

# Thread pool for running sync functions in async context
executor = ThreadPoolExecutor(max_workers=4)

# In-memory storage for job results (use Redis or database in production)
job_results: Dict[str, Dict[str, Any]] = {}


# Lifespan context manager for startup and shutdown logic
@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Handles application startup and shutdown events.
    """
    logger.info("Starting Insurance Policy Scrapper System")
    logger.info(f"Model Provider: {settings.model_provider}")
    logger.info(f"Root Folder: {settings.root_folder}")

    root_path = Path(settings.root_folder)
    if not root_path.exists():
        logger.error(f"Root folder '{settings.root_folder}' does not exist.")
        raise RuntimeError(
            "Please update the ROOT_FOLDER in your .env file or create the directory."
        )
    yield
    logger.info("Shutting down Insurance Policy Scrapper System")


app = FastAPI(
    title="Insurance Policy Scrapper Service",
    lifespan=lifespan
)


def run_policy_processing_sync(root_folder: str):
    """
    Synchronous wrapper for the processing function.
    Returns the results directly.
    """
    try:
        logger.info(f"Starting policy processing for folder: {root_folder}")
        output = process_policy_docs(root_folder,False)
        clear_folder(root_folder)

        if output:
            logger.info("Policy processing completed successfully!")
            return {
                "success": True,
                "results": output['result'],
                "output_file_path": output['output_file_path'],
                "message": "Processing completed successfully"
            }
        else:
            logger.error("Policy processing failed!")
            return {
                "success": False,
                "results": None,
                "message": "Processing failed - no results returned"
            }
    except Exception as e:
        logger.exception("An error occurred during policy processing")
        return {
            "success": False,
            "results": None,
            "message": f"Processing error: {str(e)}"
        }


def run_policy_processing_background(job_id: str, root_folder: str):
    """
    Background processing function that updates job status.
    """
    try:
        logger.info(f"Starting background processing for job: {job_id}")

        # Update status to processing
        if job_id in job_results:
            job_results[job_id]["status"] = "processing"
            job_results[job_id]["message"] = "Processing policy documents..."

        # Run the actual processing
        result = run_policy_processing_sync(root_folder)

        # Update job results
        if job_id in job_results:
            if result["success"]:
                job_results[job_id]["status"] = "completed"
                job_results[job_id]["results"] = result["results"]
                job_results[job_id]["message"] = result["message"]
                job_results[job_id]["output_file_path"] = result["output_file_path"]
            else:
                job_results[job_id]["status"] = "failed"
                job_results[job_id]["results"] = result["results"]
                job_results[job_id]["message"] = result["message"]

    except Exception as e:
        logger.exception(f"Background processing failed for job: {job_id}")
        if job_id in job_results:
            job_results[job_id]["status"] = "error"
            job_results[job_id]["results"] = None
            job_results[job_id]["message"] = f"Background processing error: {str(e)}"


@app.post("/upload/")
async def upload_file(
        file: Annotated[UploadFile, File(...)],
):
    """
    Uploads a file, processes it, and returns the results directly.
    """
    # Clear old files from the folder before uploading new one
    try:
        logger.info(f"Clearing old files from folder: {settings.root_folder}")
        clear_folder(settings.root_folder)
        logger.info("Old files cleared successfully")
    except Exception as clear_error:
        logger.warning(f"Warning: Could not clear old files: {clear_error}")
        # Continue with upload even if clearing fails

    # Sanitize the filename
    safe_filename = secure_filename(file.filename)
    file_location = Path(settings.root_folder) / safe_filename

    try:
        # Save the file
        async with aiofiles.open(file_location, "wb") as buffer:
            while content := await file.read(1024 * 1024):
                await buffer.write(content)
        logger.info(f"Uploaded file saved to {file_location}")

        # Process the file in a thread pool to avoid blocking
        logger.info("Starting policy processing...")
        loop = asyncio.get_event_loop()

        try:
            # Run processing with a timeout (adjust as needed)
            processing_result = await asyncio.wait_for(
                loop.run_in_executor(executor, run_policy_processing_sync, settings.root_folder),
                timeout=300.0  # 5 minutes timeout
            )

            if processing_result["success"]:
                return JSONResponse(
                    content={
                        "message": processing_result["message"],
                        "success": True,
                        "results": processing_result["results"],
                        "output_file_path": processing_result["output_file_path"]
                    },
                    status_code=200
                )
            else:
                return JSONResponse(
                    content={
                        "message": processing_result["message"],
                        "success": False,
                        "results": processing_result["results"]
                    },
                    status_code=422  # Unprocessable Entity
                )

        except asyncio.TimeoutError:
            logger.error("Processing timed out")
            return JSONResponse(
                content={
                    "message": "Processing timed out. The file was uploaded but processing took too long.",
                    "success": False,
                    "results": None
                },
                status_code=408  # Request Timeout
            )

    except Exception as e:
        logger.exception("Failed to process file")
        raise HTTPException(status_code=500, detail=f"Failed to process file: {str(e)}")



@app.post("/upload-async/")
async def upload_file_async(
        background_tasks: BackgroundTasks,
        file: Annotated[UploadFile, File(...)],
):
    """
    Alternative endpoint: Uploads a file and processes it in background.
    Returns job ID for status checking.
    """
    # Generate unique job ID
    job_id = str(uuid.uuid4())

    # Initialize job status
    job_results[job_id] = {
        "status": "uploading",
        "results": None,
        "message": "File upload in progress",
        "filename": file.filename
    }

    # Clear old files from the folder before uploading new one
    try:
        logger.info(f"Clearing old files from folder: {settings.root_folder}")
        clear_folder(settings.root_folder)
        logger.info("Old files cleared successfully")
    except Exception as clear_error:
        logger.warning(f"Warning: Could not clear old files: {clear_error}")
        # Continue with upload even if clearing fails

    # Sanitize the filename
    safe_filename = secure_filename(file.filename)
    file_location = Path(settings.root_folder) / safe_filename

    try:
        # Save the file
        async with aiofiles.open(file_location, "wb") as buffer:
            while content := await file.read(1024 * 1024):
                await buffer.write(content)
        logger.info(f"Uploaded file saved to {file_location}")

        # Update job status
        job_results[job_id]["status"] = "uploaded"
        job_results[job_id]["message"] = "File uploaded successfully, processing started"

    except Exception as e:
        logger.exception("Failed to save uploaded file")
        job_results[job_id]["status"] = "failed"
        job_results[job_id]["message"] = f"Upload failed: {str(e)}"
        raise HTTPException(status_code=500, detail=f"Failed to save file: {e}")
    finally:
        try:
            await file.close()
        except Exception as e:
            logger.warning(f"Error closing file: {e}")

    # Add the processing task to background tasks
    background_tasks.add_task(run_policy_processing_background, job_id, settings.root_folder)

    return JSONResponse(
        content={
            "job_id": job_id,
            "message": "File uploaded successfully. Processing has started in the background.",
            "status_url": f"/status/{job_id}",
            "filename": safe_filename
        },
        status_code=202
    )


@app.get("/status/{job_id}")
async def get_job_status(job_id: str):
    """
    Get the status and results of a processing job.
    """
    try:
        if job_id not in job_results:
            logger.warning(f"Job {job_id} not found in results")
            raise HTTPException(status_code=404, detail=f"Job {job_id} not found")

        job_data = job_results[job_id]
        logger.info(f"Retrieved status for job {job_id}: {job_data.get('status', 'unknown')}")

        # Return different status codes based on job status
        status_code = 200
        if job_data["status"] == "completed":
            status_code = 200
        elif job_data["status"] in ["failed", "error"]:
            status_code = 422
        else:  # processing, uploading, uploaded
            status_code = 202

        return JSONResponse(
            content={
                "job_id": job_id,
                "status": job_data.get("status", "unknown"),
                "message": job_data.get("message", "No message"),
                "results": job_data.get("results"),
                "filename": job_data.get("filename"),
                "output_file_path": job_data.get("output_file_path")
            },
            status_code=status_code
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error getting job status for {job_id}")
        raise HTTPException(status_code=500, detail=f"Error retrieving job status: {str(e)}")


@app.get("/jobs/")
async def list_jobs():
    """
    List all jobs and their current status.
    """
    try:
        jobs_list = []
        for job_id, job_data in job_results.items():
            jobs_list.append({
                "job_id": job_id,
                "status": job_data.get("status", "unknown"),
                "message": job_data.get("message", "No message"),
                "filename": job_data.get("filename")
            })

        return JSONResponse(
            content={
                "total_jobs": len(jobs_list),
                "jobs": jobs_list
            }
        )
    except Exception as e:
        logger.exception("Error listing jobs")
        raise HTTPException(status_code=500, detail=f"Error listing jobs: {str(e)}")


@app.delete("/jobs/{job_id}")
async def delete_job(job_id: str):
    """
    Delete a completed job from memory.
    """
    try:
        if job_id not in job_results:
            raise HTTPException(status_code=404, detail=f"Job {job_id} not found")

        del job_results[job_id]
        logger.info(f"Deleted job {job_id}")

        return JSONResponse(
            content={"message": f"Job {job_id} deleted successfully"}
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error deleting job {job_id}")
        raise HTTPException(status_code=500, detail=f"Error deleting job: {str(e)}")


# Health check endpoint
@app.get("/health")
async def health_check():
    """
    Simple health check endpoint.
    """
    return JSONResponse(
        content={
            "status": "healthy",
            "service": "Insurance Policy Scrapper Service",
            "active_jobs": len(job_results)
        }
    )


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, #host="0.0.0.0",
                port=8000)
