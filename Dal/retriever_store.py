current_retriever = None
current_bm25_retriever = None


def init_retrievers(pdf_pages, embeddings):
    global current_retriever, current_bm25_retriever
    from Dal.VectorStoreDal import create_vector_store, create_retriever_from_vector_store, create_bm25_retriever

    vector_store = create_vector_store(pdf_pages, embeddings, vector_db_backend="chroma")
    current_retriever = create_retriever_from_vector_store(vector_store)
    current_bm25_retriever = create_bm25_retriever(pdf_pages)


def get_retriever():
    return current_retriever


def get_bm25_retriever():
    return current_bm25_retriever
