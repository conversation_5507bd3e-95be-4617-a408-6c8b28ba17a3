import uuid
from typing import List, Dict, Any, Literal, Optional
from langchain_core.tools import tool

from langchain_chroma import Chroma
from langchain_community.vectorstores import FAISS
from langchain_community.retrievers import BM25Retriever
from langchain_core.vectorstores import VectorStoreRetriever
from langchain_core.documents import Document
from Constants.Config import settings
from Dal.retriever_store import get_retriever, get_bm25_retriever
from utils.LogUtils import logger


def create_vector_store(
        documents: List[Document],
        embeddings,
        vector_db_backend: Literal["faiss", "chroma"] = "faiss",
        persist_directory: Optional[str] = "./chroma_store"
):
    """Create vector store from documents using the specified backend."""
    if not documents:
        raise ValueError("No documents provided for vector store creation")

    try:
        if vector_db_backend == "faiss":
            vector_store = FAISS.from_documents(documents, embeddings)
            logger.info("FAISS vector store created successfully.")
        elif vector_db_backend == "chroma":
            vector_store = Chroma.from_documents(
                documents,
                embedding=embeddings,
                persist_directory=persist_directory,
                collection_name=uuid.uuid4().hex
            )
            logger.info(f"Chroma vector store created successfully at {persist_directory}.")
        else:
            raise ValueError(f"Unsupported vector DB backend: {vector_db_backend}")
        return vector_store
    except Exception as e:
        logger.error(f"Error creating {vector_db_backend} vector store: {e}")
        raise


def create_retriever_from_vector_store(
        vector_store,
        search_kwargs: Optional[Dict[str, Any]] = None
) -> VectorStoreRetriever:
    """Create a retriever from the given vector store."""
    if search_kwargs is None:
        search_kwargs = {"k": settings.top_k_value}

    try:
        retriever = vector_store.as_retriever(search_kwargs=search_kwargs)
        logger.info(f"Retriever created successfully with search_kwargs={search_kwargs}")
        return retriever
    except Exception as e:
        logger.error(f"Error creating retriever: {e}")
        raise


def create_bm25_retriever(
        documents: List[Document],
        k: Optional[int] = None
) -> BM25Retriever:
    """Create a BM25 retriever."""
    if not documents:
        raise ValueError("No documents provided for BM25 retriever")

    if k is None:
        k = settings.top_k_value

    try:
        texts = [doc.page_content for doc in documents]
        metadatas = [doc.metadata for doc in documents]
        bm25_retriever = BM25Retriever.from_texts(
            texts=texts,
            metadatas=metadatas,
            k=k
        )
        logger.info(f"BM25 retriever created with top_k={k}")
        return bm25_retriever
    except Exception as e:
        logger.error(f"Error creating BM25 retriever: {e}")
        raise


@tool
def retrieve_context_hybrid(
        query: str,
        vector_weight: float = 0.7,
        bm25_weight: float = 0.3
) -> List[Document]:
    """Hybrid retrieval combining vector similarity and BM25 results."""
    all_docs = []
    current_retriever = get_retriever()
    current_bm25_retriever = get_bm25_retriever()
    if current_retriever and vector_weight > 0:
        try:
            vector_docs = current_retriever.invoke(query)
            for doc in vector_docs:
                doc.metadata['retrieval_method'] = 'vector'
                doc.metadata['retrieval_weight'] = vector_weight
            all_docs.extend(vector_docs)
        except Exception as e:
            logger.error(f"Error in vector retrieval: {e}")

    if current_bm25_retriever and bm25_weight > 0:
        try:
            bm25_docs = current_bm25_retriever.invoke(query)
            for doc in bm25_docs:
                doc.metadata['retrieval_method'] = 'bm25'
                doc.metadata['retrieval_weight'] = bm25_weight
            all_docs.extend(bm25_docs)
        except Exception as e:
            logger.error(f"Error in BM25 retrieval: {e}")

    if all_docs:
        return sort_and_deduplicate_chunks(all_docs)
    return []


def sort_and_deduplicate_chunks(chunks: List[Document]) -> List[Document]:
    """Sort and deduplicate chunks based on source and page number."""
    try:
        seen = set()
        unique_chunks = []
        for doc in chunks:
            source = doc.metadata.get('source')
            page_number = doc.metadata.get('page_number', 0)
            key = (source, page_number)
            if key not in seen:
                seen.add(key)
                unique_chunks.append(doc)

        sorted_documents = sorted(
            unique_chunks,
            key=lambda doc: (
                doc.metadata.get('source', ''),
                doc.metadata.get('page_number', 0)
            )
        )
        return sorted_documents
    except Exception as e:
        logger.error(f"Unable to sort and deduplicate attachments: {e}")
        return chunks
