#!/usr/bin/env python3
"""
Test script to verify the updated policy_scrapper.py functionality
"""

import json
import os
from pathlib import Path
from InfoExtractor.policy_scrapper import process_policy_docs

def test_process_policy_docs():
    """Test the updated process_policy_docs function"""
    
    # Test with a non-existent folder
    print("Testing with non-existent folder...")
    result = process_policy_docs("non_existent_folder")
    print("Result structure:")
    print(json.dumps(result, indent=2, default=str))
    
    # Verify the expected structure
    expected_keys = ["status", "message", "total_documents", "successful_count", "failed_count", "results"]
    for key in expected_keys:
        assert key in result, f"Missing key: {key}"
    
    assert result["status"] == "Failed"
    assert result["total_documents"] == 0
    assert result["successful_count"] == 0
    assert result["failed_count"] == 0
    assert result["results"] == []
    
    print("✅ Non-existent folder test passed!")
    
    # Test with an empty folder
    print("\nTesting with empty folder...")
    empty_folder = "test_empty_folder"
    os.makedirs(empty_folder, exist_ok=True)
    
    try:
        result = process_policy_docs(empty_folder)
        print("Result structure:")
        print(json.dumps(result, indent=2, default=str))
        
        # Verify the expected structure
        for key in expected_keys:
            assert key in result, f"Missing key: {key}"
        
        assert result["total_documents"] == 0
        assert result["successful_count"] == 0
        assert result["failed_count"] == 0
        assert result["results"] == []
        
        print("✅ Empty folder test passed!")
        
    finally:
        # Clean up
        os.rmdir(empty_folder)
    
    print("\n✅ All tests passed! The updated function returns the correct structure.")

def print_result_structure_example():
    """Print an example of the expected result structure"""
    print("\n📋 Expected Result Structure:")
    example_result = {
        "status": "Success | Partial Success | Failed",
        "message": "Processing summary message",
        "total_documents": 3,
        "successful_count": 2,
        "failed_count": 1,
        "results": [
            {
                "document_path": "/path/to/document1.pdf",
                "filename": "document1",
                "status": "Success",
                "policy_type": "Auto Insurance Policy",
                "result": {"field1": "value1", "field2": "value2"},
                "output_file_path": "/path/to/output1.xlsx",
                "error_message": None
            },
            {
                "document_path": "/path/to/document2.pdf",
                "filename": "document2",
                "status": "Success",
                "policy_type": "Home Insurance Policy",
                "result": {"field1": "value1", "field2": "value2"},
                "output_file_path": "/path/to/output2.xlsx",
                "error_message": None
            },
            {
                "document_path": "/path/to/document3.pdf",
                "filename": "document3",
                "status": "Failed",
                "policy_type": None,
                "result": None,
                "output_file_path": None,
                "error_message": "Classification failed"
            }
        ]
    }
    print(json.dumps(example_result, indent=2))

if __name__ == "__main__":
    print("🧪 Testing Updated Policy Scrapper")
    print("=" * 50)
    
    try:
        test_process_policy_docs()
        print_result_structure_example()
        
        print("\n🎉 All tests completed successfully!")
        print("The updated code now returns a list of results for each document processed.")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
